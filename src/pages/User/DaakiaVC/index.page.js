import "@livekit/components-styles";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { useParams } from "react-router-dom";

import { LiveKitRoom } from "@livekit/components-react";
import { Room } from "livekit-client";
import { defaultUserChoices } from "@livekit/components-core";
import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";

import isElectron from "is-electron";
import { Prejoin } from "./customFabs/PreJoin";
import { VideoConference } from "./customFabs/VideoConference";

import { Loader } from "./components/Loader";
import { SettingsControlButton } from "./components/settings/SettingsControlButton";

import { PrejoinService } from "./services/PrejoinServices";
import { BreakoutRoomLoader } from "./components/BreakoutRoomLoader";
import TitleBar from "./components/titleBar";
import LoadingIcon from "./customFabs/icons/BreakoutAnimation2.json";

import { decoder, getLocalStorage, getMediaPermissions, setLocalStorage } from "./utils/helper";
import { constants, virtualBackground } from "./utils/constants";
import { ReactComponent as PlusIcon } from "./components/settings/icons/Plus.svg";

function DaakiaVC() {
  const maxHeight = Math.min(
    window.screen.height * window.devicePixelRatio,
    1620
  );
  const maxWidth = Math.min(
    window.screen.width * window.devicePixelRatio,
    2880
  );

  const [isWebinarMode, setIsWebinarMode] = useState(false);
  const [serverDetails, setServerDetails] = useState({});
  const [preJoinShow, setPreJoinShow] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [decodedId, setDecodedId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [isInvalidMeeting, setIsInvalidMeeting] = useState(false);
  const [meetingDetails, setMeetingDetails] = useState({});
  const [clientPreferedServerId, setClientPreferedServerId] = useState("ap1"); // eslint-disable-line no-unused-vars
  const [userChoices, setUserChoices] = useState({});
  const [isLobbyMode, setIsLobbyMode] = useState(false);
  const [isMeetingFinished, setIsMeetingFinished] = useState(false);
  const [isUsingBreakoutRoom, setIsUsingBreakoutRoom] = useState(false);
  const [isMovingToRoom, setIsMovingToRoom] = useState(false);
  const [movingRoomToken, setMovingRoomToken] = useState(null);
  const [meetingFeatures, setMeetingFeatures] = useState({});
  const [meetingUserChoices, setMeetingUserChoices] = useState({});
  const [isdataDogInitialized, setIsDataDogInitialized] = useState(false);
  const [screenShareSources, setScreenShareSources] = useState([]);
  const [isPipWindow, setIsPipWindow] = useState(false);
  const [isSelfVideoMirrored, setIsSelfVideoMirrored] = React.useState(true);
  const isElectronApp = isElectron();
  const [deviceIdAudio, setDeviceIdAudio] = useState("");
  const [deviceIdVideo, setDeviceIdVideo] = useState("");
  const [brightness, setBrightness] = useState(100);
  const [room, setRoom] = useState(
    new Room({
      dynacast: true,
      adaptiveStream: true,
      audioCaptureDefaults: {
        echoCancellation: true,
        noiseSuppression: true,
      },
      publishDefaults: {
        screenShareEncoding: {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        },
        screenShareSimulcastLayers: [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ],
      },
    })
  );
  const [isRoomFull, setIsRoomFull] = useState(false);

  // Step 1: Define state for current virtual background effect
  const [currentEffect, setCurrentEffect] = useState(null);




  // Handle brightness change and send RPC to remote participants
  const handleBrightnessChange = async (newBrightness) => {
    setBrightness(newBrightness);

    // Send brightness change to all remote participants via RPC
    if (room && room.localParticipant && room.state === "connected") {
      const remoteParticipants = Array.from(room.remoteParticipants.values());

      // Only send RPC if brightness is not default (100%) and there are remote participants
      if (newBrightness !== 100 && remoteParticipants.length > 0) {
        // Use Promise.allSettled to send RPC calls in parallel instead of sequential await in loop
        const rpcPromises = remoteParticipants.map(async (participant) => {
          try {
            // Validate participant identity
            if (!participant.identity) {
              throw new Error('Participant identity is missing');
            }

            const payload = JSON.stringify({
              brightness: newBrightness,
              participantId: room.localParticipant.identity,
              timestamp: Date.now()
            });

            console.log(`Sending brightness RPC to ${participant.identity} with payload:`, payload);

            const response = await room.localParticipant.performRpc({
              destinationIdentity: participant.identity,
              method: "setBrightness",
              payload,
              responseTimeout: 5000
            });
            console.log(`✅ Brightness RPC sent to ${participant.identity}:`, response);
            return { participant: participant.identity, success: true, response };
          } catch (error) {
            console.error(`❌ Failed to send brightness RPC to ${participant.identity}:`, error);
            return { participant: participant.identity, success: false, error: error.message };
          }
        });

        // Wait for all RPC calls to complete
        const results = await Promise.allSettled(rpcPromises);
        console.log('Brightness RPC batch completed:', results);
      } else if (remoteParticipants.length === 0) {
        console.log('No remote participants to send brightness RPC to');
      }
    }
  };


  useEffect(() => {
    room.prepareConnection("https://ap1sfu-prod.daakia.co.in");
  }, []);


  useEffect(() => {
    if (room && room.state === "connected" && brightness !== 100) {
      // Function to send brightness to all current remote participants
      const sendBrightnessToAllParticipants = async (delay = 2000) => {
        setTimeout(async () => {
          const remoteParticipants = Array.from(room.remoteParticipants.values());

          if (remoteParticipants.length > 0) {
            console.log(`Sending brightness (${brightness}%) to ${remoteParticipants.length} participants`);

            // Use Promise.allSettled to send RPC calls in parallel
            const rpcPromises = remoteParticipants.map(async (participant) => {
              // Try multiple times with increasing delays to ensure RPC method is registered
              const sendWithRetry = async (attempt = 1, maxAttempts = 3) => {
                try {
                  // Validate participant identity
                  if (!participant.identity) {
                    throw new Error('Participant identity is missing');
                  }

                  const payload = JSON.stringify({
                    brightness,
                    participantId: room.localParticipant.identity,
                    timestamp: Date.now()
                  });

                  console.log(`Sending brightness RPC to ${participant.identity} (attempt ${attempt}) with payload:`, payload);

                  const response = await room.localParticipant.performRpc({
                    destinationIdentity: participant.identity,
                    method: "setBrightness",
                    payload,
                    responseTimeout: 5000
                  });
                  console.log(`✅ Brightness RPC sent to ${participant.identity} on attempt ${attempt}:`, response);
                  return { participant: participant.identity, success: true, response };
                } catch (error) {
                  console.error(`❌ Failed to send brightness RPC to ${participant.identity} on attempt ${attempt}:`, error);

                  // Retry if it's a method not found error and we haven't exceeded max attempts
                  if (attempt < maxAttempts && (error.message.includes('UNSUPPORTED_METHOD') || error.message.includes('1400'))) {
                    console.log(`Retrying brightness RPC to ${participant.identity} in ${attempt * 2} seconds...`);
                    await new Promise((resolve) => {
                      setTimeout(() => resolve(), attempt * 2000);
                    });
                    return sendWithRetry(attempt + 1, maxAttempts);
                  }
                  return { participant: participant.identity, success: false, error: error.message };
                }
              };

              return sendWithRetry();
            });

            // Wait for all RPC calls to complete
            const results = await Promise.allSettled(rpcPromises);
            console.log('Brightness RPC batch completed:', results);
          } else {
            console.log('No remote participants to send brightness RPC to');
          }
        }, delay);
      };

      // Send to existing participants
      sendBrightnessToAllParticipants(2000);

      // Also send when new participants join (with longer delay to ensure they're ready)
      const handleParticipantConnected = () => {
        console.log('New participant connected, sending brightness after delay...');
        sendBrightnessToAllParticipants(5000);
      };

      room.on('participantConnected', handleParticipantConnected);

      return () => {
        room.off('participantConnected', handleParticipantConnected);
      };
    }
  }, [room?.state, brightness]); // Watch BOTH room state AND brightness changes

  useEffect(() => {
    const item = localStorage.getItem("lk-user-choices");
    if (!item) {
      setUserChoices(defaultUserChoices);
    }
    setUserChoices(JSON.parse(item));
  }, []);

  const { id } = useParams();

  useEffect(() => {
    if (id) {
      setDecodedId(() => decoder(id));
    }
  }, [id]);

  useEffect(() => {
    if (!isdataDogInitialized) {
      datadogLogs.init({
        clientToken: constants.DATA_DOG_TOKEN,
        site: constants.DATADOG_SITE,
        service: constants.DATADOG_LOG_SERVICE,
        env: constants.DATA_DOG_ENV,
        // version: __COMMIT_HASH__,
        forwardErrorsToLogs: false,
        sessionSampleRate: 100,
      });
      datadogRum.init({
        applicationId: constants.DATA_DOG_RUM_APPLICATION_ID,
        clientToken: constants.DATA_DOG_RUM_TOKEN,
        // `site` refers to the Datadog site parameter of your organization
        // see https://docs.datadoghq.com/getting_started/site/
        site: constants.DATADOG_SITE,
        service: constants.DATADOG_RUM_SERVICE,
        env: constants.DATA_DOG_ENV,
        // Specify a version number to identify the deployed version of your application in Datadog
        // version: '1.0.0',
        sessionSampleRate: 100,
        sessionReplaySampleRate: 100,
        trackUserInteractions: true,
        trackResources: true,
        trackLongTasks: true,
        defaultPrivacyLevel: "mask-user-input",
      });

      setIsDataDogInitialized(true);
    }
  }, []);

  // Fetch for screen share sources in desktop app
  useEffect(() => {
    const getSourcesOrError = async () => {
      try {
        const sourcesOrError = await window.electronAPI.ipcRenderer.invoke(
          "get-screenshare-sources"
        );
        if (sourcesOrError.error) {
          console.error("Error getting sources:", sourcesOrError.message);
          // Handle error (e.g., show notification, update state)
        } else {
          setScreenShareSources(sourcesOrError);
        }
      } catch (error) {
        console.error(
          "Unexpected error while fetching screen share sources:",
          error
        );
        // Handle unexpected errors
      }
    };



    if (isElectron()) {
      getSourcesOrError();
      getMediaPermissions();
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (!decodedId) return;
      try {
        const response = await PrejoinService.getMeetingDetails(decodedId);

        if (response.success === 0) {
          setIsInvalidMeeting(() => true);
          return;
        }
        setIsHost(() => response?.data?.is_host);
        const meetingData = response.data;
        const endDate = moment(meetingData?.end_date);
        const currentDate = moment();

        if (currentDate.isAfter(endDate)) {
          setIsMeetingFinished(true);
          return;
        }

        setIsPasswordProtected(() => response?.data?.is_password);
        setMeetingDetails(() => response?.data);
        setIsWebinarMode(
          () => response?.data?.event_type.toLowerCase() === "webinar"
        );
        setIsLobbyMode(() => response?.data?.is_lobby_mode);
        setLocalStorage(constants.MEETING_DETAILS, response?.data);
        const featureResponse = await PrejoinService.getMeetingFeatures(
          response?.data?.host_subscription_id || 2
        );
        if (featureResponse.success === 1) {
          setMeetingFeatures(() => featureResponse?.data);
        }
        if (
          response?.data?.meeting_logs?.session_participants >=
          Number(featureResponse?.data?.audio_video_conference)
        ) {
          setIsRoomFull(true);
        }
      } catch (error) {
        console.error("Error getting meeting details:", error);
        setIsInvalidMeeting(() => true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [decodedId]);


  useEffect(() => {
    if (!room || !isMovingToRoom || !movingRoomToken) return;
    room.disconnect();
    setServerDetails({ ...serverDetails, token: null });
    setRoom(
      new Room({
        dynacast: true,
        adaptiveStream: true,
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
        },
        publishDefaults: {
          screenShareEncoding: {
            maxBitrate: 1_000_000,
            maxFramerate: 5,
          },
          screenShareSimulcastLayers: [
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 800_000,
                maxFramerate: 1,
              },
            },
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 400_000,
                maxFramerate: 1,
              },
            },
          ],
        },
      })
    );
    setServerDetails({ ...serverDetails, token: movingRoomToken });
    setMovingRoomToken(null);
    setTimeout(() => {
      setIsMovingToRoom(false);
    }, 3000);
  }, [isMovingToRoom, movingRoomToken]);

  useEffect(() => {
    if (isUsingBreakoutRoom) {
      const meetingChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
      if (meetingChoice) {
        setMeetingUserChoices(meetingChoice);
      }
    }
  }, [isUsingBreakoutRoom]);

  const [backgrounds, setBackgrounds] = useState([
    {
      heading: "Custom",
      effects: [
        {
          label: "Upload",
          icon: <PlusIcon />,
          value: "Upload",
        },
      ],
    },
    ...virtualBackground,
  ]);

  return isLoading ? (
    <div className="loader-container">
      <Loader
        heading="Please wait for a moment"
        description="We are fetching the meeting details for you."
        isLoading
      />
    </div>
  ) : isRoomFull ? (
    <div className="loader-container">
      <Loader
        heading="Room is Full!"
        description={
          isWebinarMode
            ? "The slot for this webinar is full. Please try after some time"
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isInvalidMeeting ? (
    <div className="loader-container">
      <Loader
        heading="No Meeting Found!"
        description="Please check the meeting link and try again."
        isLoading={false}
      />
    </div>
  ) : isMeetingFinished ? (
    <div className="loader-container">
      <Loader
        heading="Meeting has been Ended!"
        description={
          isHost
            ? "You can start a new meeting."
            : "Please contact the host for more information."
        }
        isLoading={false}
      />
    </div>
  ) : isMovingToRoom ? (
    <div className="loader-container">
      <BreakoutRoomLoader
        heading="Room Hop!"
        description="Just a sec! We're moving you to your next adventure."
        isLoading
        icon={LoadingIcon}
        rootClass="breakout-room-loader"
      />
    </div>
  ) : preJoinShow ? (
    <Prejoin
      setServerDetails={setServerDetails}
      id={decodedId}
      setPreJoinShow={setPreJoinShow}
      // setIsHost={setIsHost}
      isHost={isHost}
      isPasswordProtected={isPasswordProtected}
      meetingDetails={meetingDetails}
      setClientPreferedServerId={setClientPreferedServerId}
      userChoices={userChoices}
      setUserChoices={setUserChoices}
      isLobbyMode={isLobbyMode}
      isWebinarMode={isWebinarMode}
      setIsPipWindow={setIsPipWindow}
      isPipWindow={isPipWindow}
      room={room}
      backgrounds={backgrounds}
      setBackgrounds={setBackgrounds}
      isSelfVideoMirrored={isSelfVideoMirrored}
      setIsSelfVideoMirrored={setIsSelfVideoMirrored}
      deviceIdAudio={deviceIdAudio}
      setDeviceIdAudio={setDeviceIdAudio}
      deviceIdVideo={deviceIdVideo}
      setDeviceIdVideo={setDeviceIdVideo}
      brightness={brightness}
      onBrightnessChange={handleBrightnessChange}
      currentEffect={currentEffect}
      setCurrentEffect={setCurrentEffect}
    />
  ) : (
    !preJoinShow &&
    serverDetails.token &&
    serverDetails.serverUrl && (
      <LiveKitRoom
        room={room}
        video={
          isUsingBreakoutRoom
            ? meetingUserChoices.video
            : isWebinarMode && !isHost
            ? false
            : userChoices.videoEnabled
            ? { deviceId: userChoices.videoDeviceId }
            : false
        }
        audio={
          isUsingBreakoutRoom
            ? meetingUserChoices.audio
            : isWebinarMode && !isHost
            ? false
            : userChoices.audioEnabled
            ? { deviceId: userChoices.audioDeviceId }
            : false
        }
        token={serverDetails.token}
        serverUrl={serverDetails.serverUrl}
        data-lk-theme="default"
        // style={{ height: "100vh" }}
      >
        {isElectronApp && (
          <TitleBar
            setIsPipWindow={setIsPipWindow}
            isPipWindow={isPipWindow}
            title={meetingDetails?.event_name}
          />
        )}
        <VideoConference
          room={room}
          SettingsComponent={SettingsControlButton}
          maxHeight={maxHeight}
          maxWidth={maxWidth}
          id={decodedId}
          isHost={isHost}
          meetingDetails={meetingDetails}
          clientPreferedServerId={clientPreferedServerId}
          isMeetingFinished={isMeetingFinished}
          setIsMeetingFinished={setIsMeetingFinished}
          setIsMovingToRoom={setIsMovingToRoom}
          setMovingRoomToken={setMovingRoomToken}
          isMovingToRoom={isMovingToRoom}
          meetingFeatures={meetingFeatures}
          isWebinarMode={isWebinarMode}
          setIsUsingBreakoutRoom={setIsUsingBreakoutRoom}
          token={serverDetails.token}
          isElectronApp={isElectronApp}
          screenShareSources={screenShareSources}
          isPipWindow={isPipWindow}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          backgrounds={backgrounds}
          brightness={brightness}
          onBrightnessChange={handleBrightnessChange}
          setBackgrounds={setBackgrounds}
          deviceIdAudio={deviceIdAudio}
          setDeviceIdAudio={setDeviceIdAudio}
          deviceIdVideo={deviceIdVideo}
          setDeviceIdVideo={setDeviceIdVideo}
          currentEffect={currentEffect}
          setCurrentEffect={setCurrentEffect}
        />
      </LiveKitRoom>
    )
  );
}

export default DaakiaVC;
