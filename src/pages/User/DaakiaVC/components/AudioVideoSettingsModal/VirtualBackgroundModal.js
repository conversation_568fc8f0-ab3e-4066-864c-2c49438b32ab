import { Col, Modal, Row, Progress, Slider, Checkbox } from "antd";
import React, { useEffect, useRef, useState } from "react";
import "./VirtualBackgroundModal.scss";
import axios from "axios";
import { datadogLogs } from "@datadog/browser-logs";
import { CloseOutlined, CaretUpOutlined, CaretDownOutlined } from "@ant-design/icons";
import { ReactComponent as BackgroundLightIcon } from "./Assets/bgLight.svg";
import { ReactComponent as MirrorSelfIcon } from "./Assets/mirrorSelf.svg";
import { SettingsMenuServices } from "../../services/SettingsMenuServices";
// import { noEffect } from "../../utils/virtualBackground";
import { constants } from "../../utils/constants";
import * as routes from "../../API/Endpoints/routes";
import { getLocalStorageToken } from "../../utils/helper";
import Switch from "../../../../../components/Antd/Switch/index.ant";
import { ReactComponent as BackgroundEffectsIcon } from "../../assets/icons/Background_Effects.svg";
import { ReactComponent as AdjustBrightnessIcon } from "../../assets/icons/Adjust_brightness.svg";
import { ReactComponent as ChooseAvatarIcon } from "../../assets/icons/Choose_Avatar.svg";
import { ReactComponent as MirrorViewIcon } from "../../assets/icons/Mirror_view.svg";

export default function VirtualBackgroundModal({
  open,
  setOpen,
  backgrounds,
  setBackgrounds,
  onEffectSelected,
  videoTrack,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  currentEffect,
  brightness = 100,
  onBrightnessChange,
}) {
  const videoRef = useRef(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [activeTab, setActiveTab] = useState(null);
  const [selectedBackground, setSelectedBackground] = useState(null);
  const [showBackgroundGrid, setShowBackgroundGrid] = useState(false);
  const [blurOptions, setBlurOptions] = useState({
    noBlur: true,
    lightBlur: false,
    heavyBlur: false
  });

  useEffect(() => {
    let mounted = true;

    const setupVideo = async () => {
      if (!videoTrack || !videoRef.current) return;

      try {
        // Ensure the track is enabled and unmuted
        if (!videoTrack.isEnabled) {
          await videoTrack.enable();
        }
        if (videoTrack.isMuted) {
          await videoTrack.unmute();
        }

        // Attach the track to the video element
        videoTrack.attach(videoRef.current);

        // Start playing the video
        if (videoRef.current) {
          await videoRef.current.play().catch(console.error);
        }
      } catch (error) {
        console.error('Error setting up video preview:', error);
      }
    };

    if (mounted && open) {
      setupVideo();
    }

    return () => {
      mounted = false;
      if (videoRef.current) {
        // Clean up the video element
        const videoElement = videoRef.current;
        if (videoElement.srcObject) {
          videoElement.srcObject = null;
        }
      }
    };
  }, [videoTrack, open, currentEffect]);

  const fetchVirtualBackgrounds = async () => {
    try {
      const response = await SettingsMenuServices.getVirtualBackground();
      if (response.success === 1) {
        const { data } = response;
        const custom = backgrounds.find(
          (category) => category.heading === "Custom"
        );
        data.forEach((item, index) => {
          if (item.id !== null) {
            const isUrlPresent = custom.effects.some(
              (effect) => effect.icon === item.url
            );
            if (!isUrlPresent) {
              custom.effects.unshift({
                label: `Custom ${index + 1}`,
                icon: item.url,
                value: `CT_${index + 1}`,
                id: item.id,
              });
            }
          }
        });
        setBackgrounds([...backgrounds]);
      }
    } catch (error) {
      console.error("Error fetching virtual backgrounds", error);
    }
  };

  useEffect(() => {
    fetchVirtualBackgrounds();
  }, []);

  // Reset to main menu when modal opens
  useEffect(() => {
    if (open) {
      setActiveTab(null);
    }
  }, [open]);

  // Sync blur options and selected background with current effect
  useEffect(() => {
    if (currentEffect) {
      if (currentEffect.type === 'blur') {
        const newBlurOptions = {
          noBlur: false,
          lightBlur: false,
          heavyBlur: false
        };

        if (currentEffect.value === 5) {
          newBlurOptions.lightBlur = true;
        } else if (currentEffect.value === 20) {
          newBlurOptions.heavyBlur = true;
        }

        setBlurOptions(newBlurOptions);
        setSelectedBackground(null); // Clear background selection when blur is active
      } else if (currentEffect.type === 'background') {
        // Find the background name for the current effect
        let foundBackground = null;
        for (const category of backgrounds) {
          const effect = category.effects.find(e =>
            e.value === currentEffect.value || e.icon === currentEffect.value
          );
          if (effect) {
            foundBackground = effect.label;
            break;
          }
        }
        setSelectedBackground(foundBackground);

        // Clear blur options when background is active
        setBlurOptions({
          noBlur: false,
          lightBlur: false,
          heavyBlur: false
        });
      } else {
        // If current effect is 'none', set no blur as selected
        setBlurOptions({
          noBlur: true,
          lightBlur: false,
          heavyBlur: false
        });
        setSelectedBackground(null);
      }
    } else {
      // No current effect, set no blur as selected
      setBlurOptions({
        noBlur: true,
        lightBlur: false,
        heavyBlur: false
      });
      setSelectedBackground(null);
    }
  }, [currentEffect, backgrounds]);

  // Apply brightness effect to modal video element when brightness changes
  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.style.filter = `brightness(${brightness}%)`;
    }
  }, [brightness]);

  const handleUpload = async () => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "image/*";

    fileInput.onchange = async (event) => {
      const file = event.target.files[0];

      if (file) {
        try {
          setIsUploading(true);
          setUploadProgress(0);

          const formData = new FormData();
          formData.append("image", file);

          const response = await axios.post(
            `${constants.STAG_BASE_URL}${routes.Endpoints.set_virtual_background.url}`,
            formData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: `${getLocalStorageToken()}`,
              },
              onUploadProgress: (progressEvent) => {
                const percentCompleted = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
                setUploadProgress(percentCompleted);
              },
            }
          );

          if (response.data.success === 0) {
            datadogLogs.logger.error("Error in uploading virtual background", {
              response,
              payload: file,
              endpoint: routes.Endpoints.set_virtual_background.url,
            });
            return;
          }

          datadogLogs.logger.info("Success in uploading virtual background", {
            response,
            payload: file,
            endpoint: routes.Endpoints.set_virtual_background.url,
          });

          const uploadedImageUrl = response.data.data?.url;

          if (!uploadedImageUrl) {
            console.error("No URL received from server");
            return;
          }

          for (const category of backgrounds) {
            if (category.heading === "Custom") {
              category.effects.unshift({
                label: file.name,
                icon: uploadedImageUrl,
                value: `CT_${category.effects.length + 1}`,
                id: response.data.data?.id,
              });
            }
          }
          setBackgrounds([...backgrounds]);
        } catch (error) {
          console.error("Error uploading virtual background:", error);
          datadogLogs.logger.error("Error in uploading virtual background", {
            error,
            payload: file,
            endpoint: routes.Endpoints.set_virtual_background.url,
          });
        } finally {
          setIsUploading(false);
          setUploadProgress(0);
        }
      }
    };

    fileInput.click();
  };

  const handleDelete = async (virtualBackgroundId) => {
    virtualBackgroundId = parseInt(virtualBackgroundId);

    try {
      await axios.delete(
        `${constants.STAG_BASE_URL}${routes.Endpoints.delete_virtual_background.url}`,
        {
          data: {
            virtual_background_id: virtualBackgroundId,
          },
          headers: {
            Authorization: `${getLocalStorageToken()}`,
          },
        }
      );

      for (const category of backgrounds) {
        if (category.heading === "Custom") {
          category.effects = category.effects.filter(
            (effect) => effect.id !== virtualBackgroundId
          );
        }
      }
      setBackgrounds([...backgrounds]);
    } catch (error) {
      console.error("Error deleting virtual background", error);
    }
  };

  const handleEffectClick = (_, value) => {
    if (onEffectSelected) {
      if (value === 0) {
        onEffectSelected('none', 0);
      } else if (value === "Upload") {
        handleUpload();
      } else if (typeof value === "string" && value?.startsWith("CT_")) {
        onEffectSelected('background', value);
      } else if (typeof value === "number") {
        onEffectSelected('blur', value);
      } else {
        onEffectSelected('background', value);
      }
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Menu options configuration
  const menuOptions = [
    {
      key: 'backgrounds',
      label: 'Background & Effects',
      icon: <BackgroundEffectsIcon />
    },
    {
      key: 'brightness',
      label: 'Adjust brightness',
      icon: <AdjustBrightnessIcon />
    },
    {
      key: 'mirror',
      label: 'Mirror view',
      icon: <MirrorViewIcon />
    },
    {
      key: 'avatar',
      label: 'Choose Avatar',
      icon: <ChooseAvatarIcon />
    }
  ];

  const handleMenuClick = (key) => {
    setActiveTab(key);
  };

  const handleMirrorToggle = () => {
    if (setIsSelfVideoMirrored) {
      setIsSelfVideoMirrored(!isSelfVideoMirrored);
    }
  };

  const handleBrightnessChange = (value) => {
    // Call the parent component's brightness change handler
    if (onBrightnessChange) {
      onBrightnessChange(value);
    }
    // Apply brightness filter to video element in modal
    if (videoRef.current) {
      videoRef.current.style.filter = `brightness(${value}%)`;
    }
  };

  const handleBlurChange = (blurType, checked) => {
    // Reset all blur options
    const newBlurOptions = {
      noBlur: false,
      lightBlur: false,
      heavyBlur: false
    };

    // Set the selected blur option
    if (checked) {
      newBlurOptions[blurType] = true;

      // Apply the blur effect
      if (blurType === 'noBlur') {
        handleEffectClick('none', 0);
      } else if (blurType === 'lightBlur') {
        handleEffectClick('blur', 5);
      } else if (blurType === 'heavyBlur') {
        handleEffectClick('blur', 20);
      }
    }

    setBlurOptions(newBlurOptions);
  };

  // Render option header card component
  const renderOptionHeader = (option) => {
    return (
      <div className="option-header-card">
        <div className="option-card">
          <div className="option-card-icon">
            {option.icon}
          </div>
          <div className="option-card-content">
            <div className="option-card-label">{option.label}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Modal
      open={open}
      onOk={handleClose}
      onCancel={handleClose}
      className="visual-effects-modal"
      footer={null}
      width="90%"
      style={{ maxWidth: '1000px' }}
      closeIcon={null}
      centered
    >
      <div className="visual-effects-modal-content">
        {/* Header Section */}
        <div className="modal-header">
          {activeTab && (
            <button
              className="header-back-button"
              onClick={() => setActiveTab(null)}
            >
              ←
            </button>
          )}
          <h4>
            {activeTab
              ? menuOptions.find(opt => opt.key === activeTab)?.label
              : 'Virtual Background & Effects'
            }
          </h4>
          <button
            className="header-close-button"
            onClick={handleClose}
          >
            <CloseOutlined />
          </button>
        </div>

        {/* Body Section */}
        <div className="modal-body">
          <Row gutter={[24, 16]} style={{ height: '100%' }}>
            {/* Left Column - Video Preview */}
            <Col
              xs={24}
              sm={24}
              md={14}
              lg={16}
              xl={16}
              style={{ marginBottom: { xs: 16, sm: 16, md: 0 } }}
            >
              <div className="preview-section ">

                <div className="video-preview-container">
                  <video
                    ref={videoRef}
                    className={`video-preview ${isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video"}`}
                    autoPlay
                    playsInline
                    muted
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '8px'
                    }}
                  />
                </div>


              </div>
            </Col>

            {/* Right Column - Menu and Content */}
            <Col
              xs={24}
              sm={24}
              md={10}
              lg={8}
              xl={8}
            >
              <div className="options-section">
                {activeTab === 'backgrounds' ? (
                  // Background & Effects Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'backgrounds')
                    )}
                    <div className="panel-content">
                      {isUploading && (
                        <div className="upload-progress">
                          <Progress
                            percent={uploadProgress}
                            size="small"
                            status="active"
                            strokeColor={{
                              '0%': '#108ee9',
                              '100%': '#87d068',
                            }}
                          />
                          <span className="upload-status">Uploading background...</span>
                        </div>
                      )}

                      <div className="background-effects-content">
                        {/* Choose Background Section */}
                        <div className="background-section">

                          <div
                            className="background-dropdown-trigger"
                            onClick={() => setShowBackgroundGrid(!showBackgroundGrid)}
                          >
                            <span>{selectedBackground ? 'Background Selected' : 'Choose Background'}</span>
                            <span className="dropdown-arrow">
                              {showBackgroundGrid ? <CaretUpOutlined /> : <CaretDownOutlined />}
                            </span>
                          </div>

                          {/* Background Grid - Shows when dropdown is clicked */}
                          {showBackgroundGrid && (
                            <div className="virtual-backgrounds-grid">
                              {backgrounds
                                .filter((category) => category.heading !== "Effects")
                                .map((category) => (
                                <div key={category.heading} className="vg-category-container">
                                  <div className="vg-heading primary-font">
                                    <span>{category.heading}</span>
                                  </div>
                                  <Row gutter={[8, 8]}>
                                    {category.effects.map((effect) => (
                                      <Col
                                        key={effect.label}
                                        xs={8}
                                        sm={6}
                                        md={8}
                                        lg={6}
                                        xl={6}
                                      >
                                        {category.heading === "Custom" && effect.icon ? (
                                          <div
                                            className={`vg-card ${currentEffect?.value === effect.value ? 'selected' : ''}`}
                                            onClick={() => {
                                              if (effect.value === "Upload") {
                                                handleUpload();
                                              } else if (
                                                typeof effect.value === "string" &&
                                                effect.value?.startsWith("CT_")
                                              ) {
                                                handleEffectClick('background', effect.icon);
                                              }
                                              setSelectedBackground(effect.label);
                                              setShowBackgroundGrid(false);
                                            }}
                                          >
                                            {effect.value === "Upload" ? (
                                              <div className="vg-card-image">{effect.icon}</div>
                                            ) : (
                                              <div>
                                                <img
                                                  alt={effect.label}
                                                  src={effect.icon}
                                                  className={`vg-card-image ${effect.id}`}
                                                />
                                                {effect.id && (
                                                  <span
                                                    onClick={(e) => {
                                                      handleDelete(effect.id);
                                                      e.stopPropagation();
                                                    }}
                                                    className="delete-bg"
                                                  >
                                                    ×
                                                  </span>
                                                )}
                                              </div>
                                            )}
                                          </div>
                                        ) : (
                                          <div
                                            className={`vg-card ${currentEffect?.value === effect.value ? 'selected' : ''}`}
                                            onClick={() => {
                                              handleEffectClick('background', effect.value);
                                              setSelectedBackground(effect.label);
                                              setShowBackgroundGrid(false);
                                            }}
                                          >
                                            <img
                                              alt={effect.label}
                                              src={effect.icon ? effect.icon : effect.value}
                                              className="vg-card-image"
                                            />
                                          </div>
                                        )}
                                      </Col>
                                    ))}
                                  </Row>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* Blur Background Section */}
                        <div className="blur-section">
                          <div className="section-label">Blur Background</div>
                          <div className="blur-options">
                            <Checkbox
                              checked={blurOptions.noBlur}
                              onChange={(e) => handleBlurChange('noBlur', e.target.checked)}
                            >
                              No blur
                            </Checkbox>
                            <Checkbox
                              checked={blurOptions.lightBlur}
                              onChange={(e) => handleBlurChange('lightBlur', e.target.checked)}
                            >
                              Light blur
                            </Checkbox>
                            <Checkbox
                              checked={blurOptions.heavyBlur}
                              onChange={(e) => handleBlurChange('heavyBlur', e.target.checked)}
                            >
                              Heavy blur
                            </Checkbox>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : activeTab === 'brightness' ? (
                  // Brightness Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'brightness')
                    )}
                    <div className="panel-content">
                      <div className="brightness-control">
                        <div className="control-label">
                          <BackgroundLightIcon />
                          <span>Brightness</span>
                        </div>
                        <Slider
                          min={50}
                          max={150}
                          value={brightness}
                          onChange={handleBrightnessChange}
                          tooltip={{ formatter: (value) => `${value}%` }}
                        />
                        <div className="brightness-value">{brightness}%</div>
                      </div>
                    </div>
                  </div>
                ) : activeTab === 'mirror' ? (
                  // Mirror View Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'mirror')
                    )}
                    <div className="panel-content">
                      <div className="mirror-control">
                        <div className="control-item">
                          <div className="control-info">
                            <div className="control-label">
                              <MirrorSelfIcon />
                              <span>Mirror my video</span>
                            </div>
                            <div className="control-description">
                              Mirror your video horizontally for others to see
                            </div>
                          </div>
                          <Switch
                            checked={isSelfVideoMirrored}
                            onChange={handleMirrorToggle}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : activeTab === 'avatar' ? (
                  // Avatar Content
                  <div className="content-panel">
                    {renderOptionHeader(
                      menuOptions.find(opt => opt.key === 'avatar')
                    )}
                    <div className="panel-content">
                      <div className="avatar-placeholder">
                        <div className="placeholder-icon">👤</div>
                        <div className="placeholder-text">
                          <h6>Avatar Selection</h6>
                          <p>This feature is coming soon! You&apos;ll be able to choose from various avatars to represent yourself in video calls.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Main Menu
                  <div className="menu-panel">
                    <div className="menu-grid">
                      {menuOptions.map((option) => (
                        <div
                          key={option.key}
                          className="menu-option"
                          onClick={() => handleMenuClick(option.key)}
                        >
                          <div className="menu-option-icon">
                            {option.icon}
                          </div>
                          <div className="menu-option-content">
                            <div className="menu-option-label">{option.label}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </Modal>
  );
}

