import React from 'react';
import { ReactComponent as MirrorSelfIcon } from '../Assets/mirrorSelf.svg';
import Switch from '../../../../../../components/Antd/Switch/index.ant';

const MirrorViewPanel = ({
  isSelfVideoMirrored,
  handleMirrorToggle
}) => {
  return (
    <div className="panel-content">
      <div className="mirror-control">
        <div className="control-item">
          <div className="control-info">
            <div className="control-label">
              <MirrorSelfIcon />
              <span>Mirror my video</span>
            </div>
            <div className="control-description">
              Mirror your video horizontally for others to see
            </div>
          </div>
          <Switch
            checked={isSelfVideoMirrored}
            onChange={handleMirrorToggle}
          />
        </div>
      </div>
    </div>
  );
};

export default MirrorViewPanel;
