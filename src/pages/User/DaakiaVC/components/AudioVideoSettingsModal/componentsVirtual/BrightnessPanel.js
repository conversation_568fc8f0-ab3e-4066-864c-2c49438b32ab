import React from 'react';
import { Slider } from 'antd';
import { ReactComponent as BackgroundLightIcon } from '../Assets/bgLight.svg';

const BrightnessPanel = ({
  brightness,
  handleBrightnessChange
}) => {
  return (
    <div className="panel-content">
      <div className="brightness-control">
        <div className="control-label">
          <BackgroundLightIcon />
          <span>Brightness</span>
        </div>
        <Slider
          min={50}
          max={150}
          value={brightness}
          onChange={handleBrightnessChange}
          tooltip={{ formatter: (value) => `${value}%` }}
        />
        <div className="brightness-value">{brightness}%</div>
      </div>
    </div>
  );
};

export default BrightnessPanel;
