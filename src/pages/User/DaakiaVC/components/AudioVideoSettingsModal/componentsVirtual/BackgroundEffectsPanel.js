import React from 'react';
import { Row, Col, Progress, Checkbox } from 'antd';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';

const BackgroundEffectsPanel = ({
  isUploading,
  uploadProgress,
  showBackgroundGrid,
  setShowBackgroundGrid,
  selectedBackground,
  backgrounds,
  currentEffect,
  handleUpload,
  handleEffectClick,
  setSelectedBackground,
  handleDelete,
  blurOptions,
  handleBlurChange
}) => {
  return (
    <div className="panel-content">
      {isUploading && (
        <div className="upload-progress">
          <Progress
            percent={uploadProgress}
            size="small"
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />
          <span className="upload-status">Uploading background...</span>
        </div>
      )}

      <div className="background-effects-content">
        {/* Choose Background Section */}
        <div className="background-section">
          <div
            className="background-dropdown-trigger"
            onClick={() => setShowBackgroundGrid(!showBackgroundGrid)}
          >
            <span>{selectedBackground ? 'Background Selected' : 'Choose Background'}</span>
            <span className="dropdown-arrow">
              {showBackgroundGrid ? <CaretUpOutlined /> : <CaretDownOutlined />}
            </span>
          </div>

          {/* Background Grid - Shows when dropdown is clicked */}
          {showBackgroundGrid && (
            <div className="virtual-backgrounds-grid">
              {backgrounds
                .filter((category) => category.heading !== "Effects")
                .map((category) => (
                <div key={category.heading} className="vg-category-container">
                  <div className="vg-heading primary-font">
                    <span>{category.heading}</span>
                  </div>
                  <Row gutter={[8, 8]}>
                    {category.effects.map((effect) => (
                      <Col
                        key={effect.label}
                        xs={8}
                        sm={6}
                        md={8}
                        lg={6}
                        xl={6}
                      >
                        {category.heading === "Custom" && effect.icon ? (
                          <div
                            className={`vg-card ${currentEffect?.value === effect.value ? 'selected' : ''}`}
                            onClick={() => {
                              if (effect.value === "Upload") {
                                handleUpload();
                              } else if (
                                typeof effect.value === "string" &&
                                effect.value?.startsWith("CT_")
                              ) {
                                handleEffectClick('background', effect.icon);
                              }
                              setSelectedBackground(effect.label);
                              setShowBackgroundGrid(false);
                            }}
                          >
                            {effect.value === "Upload" ? (
                              <div className="vg-card-image">{effect.icon}</div>
                            ) : (
                              <div>
                                <img
                                  alt={effect.label}
                                  src={effect.icon}
                                  className={`vg-card-image ${effect.id}`}
                                />
                                {effect.id && (
                                  <span
                                    onClick={(e) => {
                                      handleDelete(effect.id);
                                      e.stopPropagation();
                                    }}
                                    className="delete-bg"
                                  >
                                    ×
                                  </span>
                                )}
                              </div>
                            )}
                          </div>
                        ) : (
                          <div
                            className={`vg-card ${currentEffect?.value === effect.value ? 'selected' : ''}`}
                            onClick={() => {
                              handleEffectClick('background', effect.value);
                              setSelectedBackground(effect.label);
                              setShowBackgroundGrid(false);
                            }}
                          >
                            <img
                              alt={effect.label}
                              src={effect.icon ? effect.icon : effect.value}
                              className="vg-card-image"
                            />
                          </div>
                        )}
                      </Col>
                    ))}
                  </Row>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Blur Background Section */}
        <div className="blur-section">
          <div className="section-label">Blur Background</div>
          <div className="blur-options">
            <Checkbox
              checked={blurOptions.noBlur}
              onChange={(e) => handleBlurChange('noBlur', e.target.checked)}
            >
              No blur
            </Checkbox>
            <Checkbox
              checked={blurOptions.lightBlur}
              onChange={(e) => handleBlurChange('lightBlur', e.target.checked)}
            >
              Light blur
            </Checkbox>
            <Checkbox
              checked={blurOptions.heavyBlur}
              onChange={(e) => handleBlurChange('heavyBlur', e.target.checked)}
            >
              Heavy blur
            </Checkbox>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BackgroundEffectsPanel;
